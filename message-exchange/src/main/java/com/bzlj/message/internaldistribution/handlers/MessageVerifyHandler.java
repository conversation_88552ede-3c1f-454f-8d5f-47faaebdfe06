package com.bzlj.message.internaldistribution.handlers;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.bzlj.message.common.exception.ValidationException;
import com.bzlj.message.common.util.JsonUtils;
import com.bzlj.message.internaldistribution.dto.MessageSendDTO;
import com.bzlj.message.internaldistribution.dto.QueueSendDTO;
import com.bzlj.message.internaldistribution.entity.mongo.MessageException;
import com.bzlj.message.internaldistribution.entity.mongo.TopicMap;
import com.bzlj.message.internaldistribution.enums.MessageExceptionEnum;
import com.bzlj.message.internaldistribution.queue.QueueManagement;
import com.bzlj.message.internaldistribution.repository.mongo.MessageExceptionRepository;
import com.bzlj.message.internaldistribution.repository.mongo.TopicMapRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.Map;

/**
 * 消息中心消息验证处理器
 *
 * <AUTHOR>
 * @date 2025/5/17 12:11
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageVerifyHandler {
    private final TopicMapRepository topicMapRepository;
    private final MessageExceptionRepository messageExceptionRepository;


    /**
     * 消息中心消息验证入队
     * 使用 Jackson 基于 JsonUtils 工具类实现
     *
     * @param jsonStr     消息JSON字符串
     * @param receiveTime 接收时间
     */
//    @Async
    public void verifyHandler(String jsonStr, LocalDateTime receiveTime) {
        String telegramId = "";
        TopicMap isExist = null;
        try {
            log.info("消息中心入参电文:{}", jsonStr);

            // 使用 JsonUtils 解析 JSON
            JsonNode rootNode = JsonUtils.toJsonNode(jsonStr);

            // 检查是否有 messageBody 字段
            JsonNode messageBodyNode = rootNode.get("messageBody");
            if (messageBodyNode == null || messageBodyNode.isNull()) {
                insertMessageException(null,
                        jsonStr,
                        "无效消息,没有 messageBody 字段",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }
            JsonNode mqServiceParamNode = rootNode.get("__mqServiceParam__");
            if (mqServiceParamNode == null || mqServiceParamNode.isNull()) {
                insertMessageException(null,
                        jsonStr,
                        "无效消息,没有 __mqServiceParam__ 字段",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            // 解析 messageBody 内容
            String messageBodyStr = messageBodyNode.asText();
            JsonNode messageBodyJsonNode = JsonUtils.toJsonNode(messageBodyStr);

            // 检查 serviceId 和 __blocks__ 字段
            JsonNode telegramIdNode = mqServiceParamNode.get("telegramId");
            JsonNode blocksNode = messageBodyJsonNode.get("__blocks__");

            if (telegramIdNode == null || telegramIdNode.isNull() ||
                blocksNode == null || blocksNode.isNull()) {
                insertMessageException(null,
                        jsonStr,
                        "无效消息,没有 'serviceId' 或 '__blocks__' 字段",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            telegramId = telegramIdNode.asText();
            isExist = topicMapRepository.findByTelegraphTextCode(telegramId);
            if (ObjUtil.isNull(isExist)) {
                insertMessageException(isExist,
                        jsonStr,
                        "无效消息,不再电文映射关系中",
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            // 数据格式验证
            try {
                validateJson(messageBodyStr);
            } catch (ValidationException e) {
                insertMessageException(isExist,
                        jsonStr,
                        "JSON 验证失败: " + e.getMessage(),
                        MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                        receiveTime);
                return;
            }

            // 验证通过后加入队列
            // 将telegramId放到blocksNode中
            JsonNode modifiedBlocksNode = addTelegramIdToBlocks(blocksNode, telegramId);

            String blocksStr = JsonUtils.toJson(modifiedBlocksNode);
            String blocks = getSendDTO(telegramId, isExist.getType(), blocksStr);
            QueueSendDTO queueSendDTO = new QueueSendDTO()
                    .setTopic(isExist.getTopic())
                    .setId(telegramId)
                    .setPayload(blocks)
                    .setReceiveTime(receiveTime);
            if (!QueueManagement.mainQueue().offer(queueSendDTO)) {
                insertMessageException(isExist,
                        jsonStr,
                        "队列加入失败",
                        MessageExceptionEnum.ENQUEUE_EXCEPTION.getType(),
                        receiveTime);
            }

        } catch (Exception e) {
            if (StrUtil.isNotBlank(telegramId) && ObjUtil.isNull(isExist))
                isExist = topicMapRepository.findByTelegraphTextCode(telegramId);
            insertMessageException(isExist,
                    jsonStr,
                    e.getMessage(),
                    MessageExceptionEnum.NOT_SEND_EXCEPTION.getType(),
                    receiveTime);
        }
    }


    /**
     * 数据校验
     *
     * @param jsonString
     * @throws ValidationException
     */
    public void validateJson(String jsonString) throws ValidationException {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode rootNode = objectMapper.readTree(jsonString);

            // 1. 检查 __blocks__ 是否存在
            if (!rootNode.has("__blocks__")) {
                throw new ValidationException("Missing '__blocks__' field");
            }
            JsonNode blocksNode = rootNode.get("__blocks__");

            // 2. 遍历所有表（key-value 形式）
            Iterator<Map.Entry<String, JsonNode>> tables = blocksNode.fields();
            while (tables.hasNext()) {
                Map.Entry<String, JsonNode> tableEntry = tables.next();
                String tableName = tableEntry.getKey();
                JsonNode tableNode = tableEntry.getValue();

                // 3. 检查 columns 和 rows 是否存在
                if (!tableNode.has("meta") || !tableNode.get("meta").has("columns")) {
                    throw new ValidationException("'" + tableName + ".meta.columns' is missing or invalid");
                }
                if (!tableNode.has("rows") || !tableNode.get("rows").isArray()) {
                    throw new ValidationException("'" + tableName + ".rows' is missing or not an array");
                }

                JsonNode columnsNode = tableNode.get("meta").get("columns");
                JsonNode rowsNode = tableNode.get("rows");

                // 4. 检查 columns 和 rows 的长度是否匹配
                if (!rowsNode.isEmpty() && columnsNode.size() != getRowLength(rowsNode.get(0))) {
                    throw new ValidationException("'" + tableName + "' columns and rows length mismatch");
                }

                // 5. 遍历每一行数据，校验类型
                for (int rowIndex = 0; rowIndex < rowsNode.size(); rowIndex++) {
                    JsonNode row = rowsNode.get(rowIndex);
                    if (row.size() != columnsNode.size()) {
                        throw new ValidationException("'" + tableName + "' row " + rowIndex + " has incorrect number of values");
                    }

                    for (int colIndex = 0; colIndex < columnsNode.size(); colIndex++) {
                        JsonNode column = columnsNode.get(colIndex);
                        JsonNode cellValue = row.get(colIndex);

                        // 检查 column 是否有 type="N"
                        if (column.has("type") && "N".equals(column.get("type").asText())) {
                            // 必须是数字或可转换为数字
                            if (cellValue == null || !isValidNumber(cellValue)) {
                                throw new ValidationException("'" + tableName + "' column '" + getColumnIdentifier(column) + "' expects a number, but got: " + cellValue);
                            }
                        } else {
                            // 默认是字符串
                            if (cellValue == null || !cellValue.isTextual()) {
                                log.error("'" + tableName + "' column '" + getColumnIdentifier(column) + "' expects a string, but got: " + cellValue);
//                                throw new ValidationException("'" + tableName + "' column '" + getColumnIdentifier(column) + "' expects a string, but got: " + cellValue);
                            }
                        }
                    }
                }
            }

        } catch (IOException e) {
            throw new ValidationException("Invalid JSON format: " + e.getMessage());
        }
    }

    /**
     * 获取 row 的长度（处理空数组情况）
     *
     * @param row
     * @return
     */
    private int getRowLength(JsonNode row) {
        if (row == null || !row.isArray()) {
            return 0;
        }
        return row.size();
    }

    /**
     * 获取 column 的标识（name 或 descName）
     *
     * @param column
     * @return
     */
    private String getColumnIdentifier(JsonNode column) {
        if (column.has("name")) {
            return column.get("name").asText();
        } else if (column.has("descName")) {
            return column.get("descName").asText();
        }
        return "column_" + column.get("pos").asInt(); // 如果都没有，用位置标识
    }

    /**
     * 异常信息落库
     *
     * @param topicMap
     * @param jsonStr        电文
     * @param exceptionsInfo 异常原因
     * @param type           type  异常类型
     * @param receiveTime    接收时间
     */
    public void insertMessageException(TopicMap topicMap,
                                       String jsonStr,
                                       String exceptionsInfo,
                                       Integer type,
                                       LocalDateTime receiveTime) {
        log.info("异常原因：{}!!!!!!!!!!!!!!", exceptionsInfo);
        if (ObjUtil.isNull(topicMap)) topicMap = new TopicMap();
        MessageException messageException = new MessageException()
                .setTopic(topicMap.getTopic())
                .setCreateTime(LocalDateTime.now())
                .setExceptionsInfo(exceptionsInfo)
                .setTelegraphTextCode(topicMap.getTelegraphTextCode()).
                setTelegraphTextBody(jsonStr)
                .setType(type).setReceiveTime(receiveTime);
        messageExceptionRepository.insert(messageException);
    }

    /**
     * 异常信息落库
     *
     * @param topic             topic
     * @param telegraphTextCode 电文号
     * @param jsonStr           电文
     * @param exceptionsInfo    异常原因
     * @param type              type  异常类型
     * @param receiveTime       接收时间
     */
    public void insertMessageException(String topic,
                                       String telegraphTextCode,
                                       String jsonStr,
                                       String exceptionsInfo,
                                       Integer type,
                                       LocalDateTime receiveTime) {
        log.info("异常原因：{}!!!!!!!!!!!!!!", exceptionsInfo);
        MessageException messageException = new MessageException()
                .setTopic(topic)
                .setCreateTime(LocalDateTime.now())
                .setExceptionsInfo(exceptionsInfo)
                .setTelegraphTextCode(telegraphTextCode).
                setTelegraphTextBody(jsonStr)
                .setType(type).setReceiveTime(receiveTime);
        messageExceptionRepository.insert(messageException);
    }

    /**
     * 投递消息组装
     *
     * @param serviceId
     * @param type
     * @param convertJson
     * @return
     */
    private String getSendDTO(String serviceId, String type, String convertJson) {
        MessageSendDTO sendDTO = new MessageSendDTO();
        sendDTO.setId(serviceId);
        sendDTO.setPayload(convertJson);
        sendDTO.setReceiptTime(DateUtil.toInstant(LocalDateTime.now()).toEpochMilli());
        sendDTO.setType(type);
        return sendDTO.toString();
    }

    /**
     * 异常信息落库
     *
     * @param queueSend      信息
     * @param exceptionsInfo 异常原因
     * @param type           type  异常类型
     * <AUTHOR>
     * @date 2025/6/11 16:36
     */
    public void insertMessageException(QueueSendDTO queueSend,
                                       String exceptionsInfo,
                                       Integer type) {
        log.info("异常原因：{}!!!!!!!!!!!!!!", exceptionsInfo);
        MessageSendDTO messageSendDTO = JsonUtils.fromJson(queueSend.getPayload(), MessageSendDTO.class);
        MessageException messageException = new MessageException()
                .setTopic(queueSend.getTopic())
                .setCreateTime(LocalDateTime.now())
                .setExceptionsInfo(exceptionsInfo)
                .setTelegraphTextCode(queueSend.getId()).
                setTelegraphTextBody(JsonUtils.toJson(messageSendDTO))
                .setType(type).setReceiveTime(queueSend.getReceiveTime());
        messageExceptionRepository.insert(messageException);
    }

    /**
     * 将 telegramId 添加到 blocksNode 中
     *
     * @param blocksNode 原始的 blocks 节点
     * @param telegramId 电报ID
     * @return 修改后的 blocks 节点
     */
    private JsonNode addTelegramIdToBlocks(JsonNode blocksNode, String telegramId) {
        try {
            // 参数验证
            if (telegramId == null || telegramId.trim().isEmpty()) {
                log.warn("telegramId 为空，不添加到 blocksNode 中");
                return blocksNode;
            }

            // 创建一个可修改的 ObjectNode 副本
            ObjectNode modifiedBlocks;

            if (blocksNode != null && blocksNode.isObject()) {
                // 如果是对象，创建副本
                modifiedBlocks = blocksNode.deepCopy();
                log.debug("使用现有 blocksNode 创建副本");
            } else {
                // 如果不是对象或为null，创建新的对象节点
                modifiedBlocks = JsonUtils.createObjectNode();
                log.warn("blocksNode 不是有效对象类型，创建新的对象节点。原始类型: {}",
                    blocksNode != null ? blocksNode.getNodeType() : "null");

                // 如果原始节点有内容但不是对象，尝试保留原始数据
                if (blocksNode != null && !blocksNode.isNull()) {
                    modifiedBlocks.set("originalData", blocksNode);
                }
            }

            // 添加 telegramId 字段
            modifiedBlocks.put("telegramId", telegramId.trim());

            log.info("成功将 telegramId: {} 添加到 blocksNode 中", telegramId);
            return modifiedBlocks;

        } catch (Exception e) {
            log.error("添加 telegramId: {} 到 blocksNode 失败: {}", telegramId, e.getMessage(), e);
            // 如果出错，返回原始节点
            return blocksNode;
        }
    }

    /**
     * 检查值是否为有效数字或可转换为数字
     * 支持多种数字格式：整数、小数、科学计数法、正负号等
     *
     * @param value JSON节点值
     * @return true 如果是数字或可转换为数字，否则 false
     */
    private boolean isValidNumber(JsonNode value) {
        if (value == null || value.isNull()) {
            log.debug("值为 null，不是有效数字");
            return false;
        }

        // 如果已经是数字类型，直接返回 true
        if (value.isNumber()) {
            log.debug("值 '{}' 已经是数字类型", value.asText());
            return true;
        }

        // 如果是字符串，尝试转换为数字
        if (value.isTextual()) {
            String textValue = value.asText().trim();

            // 空字符串不是有效数字
            if (textValue.isEmpty()) {
                log.debug("空字符串不是有效数字");
                return false;
            }

            // 检查常见的数字格式
            return isNumericString(textValue);
        }

        // 其他类型（布尔值、对象、数组等）不是有效数字
        log.debug("值类型 '{}' 不是有效的数字类型，值: {}", value.getNodeType(), value.asText());
        return false;
    }

    /**
     * 检查字符串是否可以转换为数字
     * 支持多种格式：整数、小数、科学计数法、正负号等
     *
     * @param str 待检查的字符串
     * @return true 如果可以转换为数字，否则 false
     */
    private boolean isNumericString(String str) {
        try {
            // 使用 Double.parseDouble 进行转换，支持最广泛的数字格式
            double result = Double.parseDouble(str);

            // 检查是否为有效的数字（不是 NaN 或无穷大）
            if (Double.isNaN(result) || Double.isInfinite(result)) {
                log.debug("字符串 '{}' 转换结果为 NaN 或无穷大", str);
                return false;
            }

            log.debug("字符串 '{}' 成功转换为数字: {}", str, result);
            return true;

        } catch (NumberFormatException e) {
            log.debug("字符串 '{}' 无法转换为数字: {}", str, e.getMessage());
            return false;
        }
    }
}
