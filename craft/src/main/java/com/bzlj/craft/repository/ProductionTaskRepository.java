package com.bzlj.craft.repository;

import com.bzlj.base.repository.BaseRepository;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.entity.SysDictItem;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductionTaskRepository extends BaseRepository<ProductionTask, String> {
    /**
     * 查询状态为statusCode的任务
     *
     * @param statusCode 任务状态
     * <AUTHOR>
     * @date 2025/3/24 14:22
     */
    List<ProductionTask> findByStatusCode(@NotNull SysDictItem statusCode);

    /**
     * 查询状态为statusCode的任务，预加载关联的物料和设备信息
     *
     * @param statusCode 任务状态
     * <AUTHOR>
     * @date 2025/3/24 14:22
     */
    @Query("SELECT DISTINCT t FROM ProductionTask t " +
           "LEFT JOIN FETCH t.taskMaterials tm " +
           "LEFT JOIN FETCH tm.material " +
           "LEFT JOIN FETCH t.taskEquipments te " +
           "LEFT JOIN FETCH te.equip " +
           "WHERE t.statusCode = :statusCode")
    List<ProductionTask> findByStatusCodeWithAssociations(@Param("statusCode") @NotNull SysDictItem statusCode);

    /**
     * 根据任务ID列表和状态查询任务，预加载关联的物料和设备信息
     *
     * @param taskIds 任务ID列表
     * @param statusCode 任务状态
     * <AUTHOR>
     * @date 2025/3/24 14:22
     */
    @Query("SELECT DISTINCT t FROM ProductionTask t " +
           "LEFT JOIN FETCH t.taskMaterials tm " +
           "LEFT JOIN FETCH tm.material " +
           "LEFT JOIN FETCH t.taskEquipments te " +
           "LEFT JOIN FETCH te.equip " +
           "WHERE t.taskId IN :taskIds AND t.statusCode = :statusCode")
    List<ProductionTask> findByTaskIdInAndStatusCodeWithAssociations(@Param("taskIds") List<String> taskIds, @Param("statusCode") SysDictItem statusCode);

    /**
     * 根据任务编号查询任务
     * @param taskCode
     * @return
     */
    ProductionTask findFirstByTaskCode(String taskCode);

    ProductionTask findByTaskId(String taskId);

    List<ProductionTask> findByTaskIdInAndStatusCode(List<String> taskIds,SysDictItem statusCode);

    @Query("UPDATE ProductionTask t SET t.statusCode = :status WHERE t.taskId = :taskId")
    @Modifying
    void changeStatus(
            @Param("taskId") String taskId,
            @Param("status") SysDictItem status
    );
}