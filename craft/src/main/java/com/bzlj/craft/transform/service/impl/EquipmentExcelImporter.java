package com.bzlj.craft.transform.service.impl;

import com.bzlj.craft.entity.Equipment;
import com.bzlj.craft.repository.EquipmentRepository;
import com.bzlj.craft.service.IQualityTracingService;
import com.bzlj.craft.transform.constant.ExcelImportPolicyConstants;
import com.bzlj.craft.transform.service.ExcelImportPolicy;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Service(value = ExcelImportPolicyConstants.EXCEL_IMPORT_POLICY + "equipment")
public class EquipmentExcelImporter implements ExcelImportPolicy<Equipment> {

    private final EquipmentRepository equipmentRepository;

    private final IQualityTracingService qualityTracingService;

    public EquipmentExcelImporter(EquipmentRepository equipmentRepository,IQualityTracingService qualityTracingService) {
        this.equipmentRepository = equipmentRepository;
        this.qualityTracingService = qualityTracingService;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult<Equipment> importFromExcel(InputStream inputStream){
        ImportResult<Equipment> results = new ImportResult<>();

        List<Equipment> equipments = parseExcel(inputStream);
        List<String> excelCodes = equipments.stream()
                .map(Equipment::getCode)
                .collect(Collectors.toList());

        // 批量查询已存在的编码
        List<String> existingCodes = equipmentRepository.findExistingCodes(excelCodes);

        // 过滤重复数据
        List<Equipment> newEquipments = equipments.stream()
                .filter(p -> !existingCodes.contains(p.getCode()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(newEquipments)) {
            results.setSize(0);
            results.setResults(List.of());
        }
        List<Equipment> equipmentLists = equipmentRepository.saveAll(newEquipments);
        qualityTracingService.saveEquipments(equipmentLists);
        results.setSize(equipmentLists.size());
        results.setResults(equipmentLists);
        return results;
    }

    @Override
    public List<Equipment> parseExcel(InputStream inputStream) {
        List<Equipment> equipments = new ArrayList<>();
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 默认读取第一个Sheet

            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过表头

                String code = getCellStringValue(row.getCell(0));
                String name = getCellStringValue(row.getCell(1));

                Equipment equipment = new Equipment();
                equipment.setName(name);
                equipment.setCode(code);
                equipment.setModel("-");
                equipment.setCategory("-");
                equipment.setManufacturer("-");
                equipment.setSpecification(new HashMap<>());

                equipments.add(equipment);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally{
            try {
                workbook.close();
            }catch (IOException e){
                throw new RuntimeException("Failed to close workbook", e);
            }
        }
        return equipments;
    }
}