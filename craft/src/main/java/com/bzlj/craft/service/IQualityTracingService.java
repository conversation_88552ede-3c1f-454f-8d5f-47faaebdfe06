package com.bzlj.craft.service;

import com.bzlj.craft.entity.Equipment;
import com.bzlj.craft.entity.Material;
import com.bzlj.craft.query.QualityTraceQuery;
import com.bzlj.craft.vo.qualitytrace.QualityTraceVO;

import java.util.List;
import java.util.Map;

/**
 * 质量追溯接口
 *
 * <AUTHOR>
 * @date 2025/3/24 11:15
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
public interface IQualityTracingService {
    /**
     * 初始化nebula数据
     *
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    Boolean initNebulaData();

    /**
     * 保存数据
     *
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    void saveData();

    /**
     * 保存物料信息
     *
     * @param materials
     */
    void saveMaterials(List<Material> materials);

    /**
     * 保存设备信息
     *
     * @param equipments
     */
    void saveEquipments(List<Equipment> equipments);

    /**
     * 保存生产任务信息
     *
     * @param taskIds 为空则查询所有已完成的任务
     */
    void saveTasks(List<String> taskIds);

    /**
     * 保存任务设备信息
     *
     * @param taskEquipmentMap
     */
    void saveTaskEquipment(Map<String, String> taskEquipmentMap);

    /**
     * 获取质量追溯信息
     *
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    QualityTraceVO getQualityTracing(QualityTraceQuery query);
}
