package com.bzlj.craft.service.impl;


import com.bzlj.craft.entity.*;
import com.bzlj.craft.nebula.constants.CommonNGQLConstants;
import com.bzlj.craft.nebula.entity.edge.InputMaterial;
import com.bzlj.craft.nebula.entity.edge.OutputMaterial;
import com.bzlj.craft.nebula.entity.edge.UseEquip;
import com.bzlj.craft.nebula.entity.tag.Equip;
import com.bzlj.craft.nebula.entity.tag.Task;
import com.bzlj.craft.nebula.util.NebulaUtil;
import com.bzlj.craft.query.QualityTraceQuery;
import com.bzlj.craft.repository.EquipmentRepository;
import com.bzlj.craft.repository.MaterialRepository;
import com.bzlj.craft.repository.ProductionTaskRepository;
import com.bzlj.craft.service.IQualityTracingService;
import com.bzlj.craft.util.CompletableFutureUtil;
import com.bzlj.craft.util.JsonUtils;
import com.bzlj.craft.vo.qualitytrace.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import io.jsonwebtoken.lang.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;


/**
 * 质量追溯接口实现
 *
 * <AUTHOR>
 * @date 2025/3/24 11:15
 * @motto I know I'm not smart, but I want to be a good architect. Come on
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class QualityTracingServiceImpl implements IQualityTracingService {
    private final ProductionTaskRepository productionTaskRepository;
    private final MaterialRepository materialRepository;
    private final EquipmentRepository equipmentRepository;

    /**
     * 初始化nebula数据
     *
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    @Override
    public Boolean initNebulaData() {
        log.info("开始初始化nebula数据");
        long startTime = System.currentTimeMillis();
        CompletableFutureUtil.runVoidAsync(this::saveData,
                () -> log.info("初始化数据刷入nebula成功,耗时:{}ms", System.currentTimeMillis() - startTime),
                ex -> log.error("初始化数据刷入nebula失败,耗时:{}ms", System.currentTimeMillis() - startTime, ex));
        return Boolean.TRUE;
    }

    /**
     * 保存数据
     *
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveData() {
        long materialStartTime = System.currentTimeMillis();
        // 刷入物料
        List<Material> allMaterial = materialRepository.findAll();
        CompletableFuture<Void> materialFuture = CompletableFutureUtil.runVoidAsync(() -> saveMaterials(allMaterial),
                () -> log.info("物料数据刷入nebula成功,耗时:{}ms", System.currentTimeMillis() - materialStartTime),
                ex -> log.error("物料数据刷入nebula失败,耗时:{}ms", System.currentTimeMillis() - materialStartTime, ex));
        long equipmentStartTime = System.currentTimeMillis();
        // 刷入设备
        List<Equipment> allEquipment = equipmentRepository.findAll();
        CompletableFuture<Void> equipmentFuture = CompletableFutureUtil.runVoidAsync(() -> saveEquipments(allEquipment),
                () -> log.info("设备数据刷入nebula成功,耗时:{}ms", System.currentTimeMillis() - equipmentStartTime),
                ex -> log.error("设备数据刷入nebula失败,耗时:{}ms", System.currentTimeMillis() - equipmentStartTime, ex));
        CompletableFutureUtil.allOf(materialFuture, equipmentFuture).join();
        // 刷入任务
        saveTasks(null);

    }


    /**
     * 保存物料信息
     *
     * @param materials
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    @Override
    public void saveMaterials(List<Material> materials) {
        List<com.bzlj.craft.nebula.entity.tag.Material> materialList = materials.parallelStream()
                .map(material -> {
                    com.bzlj.craft.nebula.entity.tag.Material nebulaMaterial = new com.bzlj.craft.nebula.entity.tag.Material();
                    BeanUtils.copyProperties(material, nebulaMaterial);
                    return nebulaMaterial;
                }).toList();
        NebulaUtil.batchInsert(materialList);
    }

    /**
     * 保存设备信息
     *
     * @param equipments
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    @Override
    public void saveEquipments(List<Equipment> equipments) {
        List<Equip> equipList = equipments.parallelStream().map(equipment -> {
            Equip equip = new Equip();
            equip.setEquipId(equipment.getId());
            equip.setEquipName(equipment.getName());
            equip.setEquipCode(equipment.getCode());
            return equip;
        }).toList();
        NebulaUtil.batchInsert(equipList);
    }

    /**
     * 保存任务信息
     *
     * @param taskIds 为空则查询所有已完成的任务
     * <AUTHOR>
     * @date 2025/3/24 14:13
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTasks(List<String> taskIds) {
        SysDictItem sysDictItem = new SysDictItem();
        sysDictItem.setItemCode("completed");
        List<ProductionTask> completedTasks;
        if (Collections.isEmpty(taskIds)) {
            completedTasks = productionTaskRepository.findByStatusCodeWithAssociations(sysDictItem);
        } else {
            completedTasks = productionTaskRepository.findByTaskIdInAndStatusCodeWithAssociations(taskIds, sysDictItem);
        }
        if (Collections.isEmpty(completedTasks)) {
            return;
        }
        completedTasks.forEach(productionTask -> {
            Task task = new Task();
            BeanUtils.copyProperties(productionTask, task);
            task.setPlantId(productionTask.getPlant().getPlantCode());
            NebulaUtil.insert(task);
            // 构建关系 任务-物料
            for (TaskMaterial taskMaterial : productionTask.getTaskMaterials()) {
                CompletableFutureUtil.runVoidAsync(() -> saveTaskMaterialRelation(taskMaterial.getRelationType(),
                                taskMaterial.getMaterial().getMaterialId(),
                                task.getTaskId()),
                        () -> log.info("任务物料关系刷入nebula成功"),
                        ex -> log.error("任务物料关系刷入nebula失败", ex));
            }
            // 构建关系 任务-设备
            for (TaskEquipment taskEquipment : productionTask.getTaskEquipments()) {
                CompletableFutureUtil.runVoidAsync(() -> NebulaUtil.insertEdge(task.getTaskId(),
                                taskEquipment.getEquip().getId(),
                                new UseEquip()),
                        () -> log.info("任务设备关系刷入nebula成功"),
                        ex -> log.error("任务设备关系刷入nebula失败", ex));
            }
        });
    }

    /**
     * 保存任务物料关系
     *
     * @param relationType 关系类型
     * @param materialId   物料id
     * @param taskId       任务id
     * <AUTHOR>
     * @date 2025/3/24 14:20
     */
    private void saveTaskMaterialRelation(Boolean relationType, String materialId, String taskId) {
        if (relationType) {
            NebulaUtil.insertEdge(taskId,
                    materialId,
                    new OutputMaterial());
        } else {
            NebulaUtil.insertEdge(materialId,
                    taskId,
                    new InputMaterial());

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTaskEquipment(Map<String, String> taskEquipmentMap) {
        taskEquipmentMap.forEach((taskId, equipId) -> {
            NebulaUtil.insertEdge(taskId, equipId, new UseEquip());
        });
    }

    /**
     * 获取质量追溯信息
     *
     * @param query
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    @Override
    public QualityTraceVO getQualityTracing(QualityTraceQuery query) {
        QualityTraceVO result = new QualityTraceVO();
        if (StringUtils.isBlank(query.getPlantId())) {
            queryAll(query, result);
        } else {
            queryByPlantId(query, result);
        }
        return result;
    }

    /**
     * 厂内程追溯
     *
     * @param query  查询条件
     * @param result 返回结果
     * <AUTHOR>
     * @date 2025/6/23 15:21
     */
    private void queryByPlantId(QualityTraceQuery query, QualityTraceVO result) {
        //1. 获取产出物料的任务
        List<TraceTaskVO> selectTasks = getSelectTasks(query);
        result.setSelectTasks(selectTasks);
        final ConcurrentLinkedDeque<TracePathVO> paths = new ConcurrentLinkedDeque<>();
        final ConcurrentHashMap<String, Object> pathCache = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, TraceTaskVO> taskCache = new ConcurrentHashMap<>();
        for (TraceTaskVO selectTask : selectTasks) {
            taskCache.put(selectTask.getTaskId(), selectTask);
        }
        selectTasks.parallelStream().forEach(traceTaskVO -> {
            addPreviousTask(paths, traceTaskVO, query.getPlantId(), pathCache, taskCache);
            addNextTask(paths, traceTaskVO, query.getPlantId(), pathCache, taskCache);
        });
        result.setPath(paths.stream().toList());
    }

    /**
     * 全流程追溯
     *
     * @param query  查询条件
     * @param result 返回结果
     * <AUTHOR>
     * @date 2025/6/23 15:21
     */
    private void queryAll(QualityTraceQuery query, QualityTraceVO result) {
        //1. 获取产出物料的任务
        List<TraceTaskVO> selectTasks = getSelectTasks(query);
        result.setSelectTasks(selectTasks);
        final ConcurrentLinkedDeque<TracePathVO> paths = new ConcurrentLinkedDeque<>();
        final ConcurrentHashMap<String, Object> pathCache = new ConcurrentHashMap<>();
        final ConcurrentHashMap<String, TraceTaskVO> taskCache = new ConcurrentHashMap<>();
        for (TraceTaskVO selectTask : selectTasks) {
            taskCache.put(selectTask.getTaskId(), selectTask);
        }
        selectTasks.parallelStream().forEach(traceTaskVO -> {
            addPreviousTask(paths, traceTaskVO, null, pathCache, taskCache);
            addNextTask(paths, traceTaskVO, null, pathCache, taskCache);
        });
        result.setPath(paths.stream().toList());
    }

    /**
     * 查找后节点
     *
     * @param traceTaskVO 当前节点
     * @param pathCache   缓存
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private void addNextTask(ConcurrentLinkedDeque<TracePathVO> paths,
                             TraceTaskVO traceTaskVO,
                             String plantId,
                             ConcurrentHashMap<String, Object> pathCache,
                             ConcurrentHashMap<String, TraceTaskVO> taskCache) {
        String nGQL = StringUtils.isBlank(plantId) ?
                CommonNGQLConstants.getQueryQualityTraceNextPathByTaskIdNGQL(traceTaskVO.getTaskId()) :
                CommonNGQLConstants.getQueryQualityTraceNextPathByTaskIdAndPlantIdNGQL(plantId, traceTaskVO.getTaskId());
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> pathMap : execute) {
            TracePathVO path = new TracePathVO();
            dealStartNode(traceTaskVO, taskCache, pathMap, path);
            dealEndNode(traceTaskVO, taskCache, pathMap, path);
            // 去除环形路径
            String cacheKey = path.getStart().getTaskId() + "_" + path.getEnd().getTaskId();
            if (pathCache.containsKey(cacheKey)) {
                continue;
            }
            pathCache.put(cacheKey, path);
            paths.addLast(path);
            addNextTask(paths, path.getEnd(), plantId, pathCache, taskCache);
        }
    }

    /**
     * 查找前节点
     *
     * @param traceTaskVO 当前节点
     * @param pathCache   缓存
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private void addPreviousTask(ConcurrentLinkedDeque<TracePathVO> paths,
                                 TraceTaskVO traceTaskVO,
                                 String plantId,
                                 ConcurrentHashMap<String, Object> pathCache,
                                 ConcurrentHashMap<String, TraceTaskVO> taskCache) {
        String nGQL = StringUtils.isBlank(plantId) ? CommonNGQLConstants.getQueryQualityTracePrePathByTaskIdNGQL(traceTaskVO.getTaskId()) :
                CommonNGQLConstants.getQueryQualityTracePrePathByTaskIdAndPlantIdNGQL(plantId, traceTaskVO.getTaskId());
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> pathMap : execute) {
            TracePathVO path = new TracePathVO();
            dealStartNode(traceTaskVO, taskCache, pathMap, path);
            dealEndNode(traceTaskVO, taskCache, pathMap, path);
            // 去除环形路径
            String cacheKey = path.getStart().getTaskId() + "_" + path.getEnd().getTaskId();
            if (pathCache.containsKey(cacheKey)) {
                continue;
            }
            pathCache.put(cacheKey, path);
            paths.addFirst(path);
            addPreviousTask(paths, path.getStart(), plantId, pathCache, taskCache);
        }
    }

    /**
     * 处理结束节点
     *
     * @param traceTaskVO
     * @param taskCache
     * @param pathMap
     * @param path
     * <AUTHOR>
     * @date 2025/3/25 16:55
     */
    private void dealEndNode(TraceTaskVO traceTaskVO,
                             ConcurrentHashMap<String, TraceTaskVO> taskCache,
                             Map<String, Object> pathMap,
                             TracePathVO path) {
        Map<String, Object> endMap = (Map) pathMap.get("e");
        Map<String, Object> endProperties = (Map) endMap.get("properties");
        Map<String, Object> endTask = (Map) endProperties.get("task");
        TraceTaskVO endTraceTaskVO;
        if (taskCache.containsKey(endTask.get("taskId"))) {
            endTraceTaskVO = taskCache.get(endTask.get("taskId"));
            path.setEnd(endTraceTaskVO);
            return;
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        endTask.forEach((k, v) -> {
            dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
        });
        endTraceTaskVO = JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceTaskVO.class);
        endTraceTaskVO.setInputMaterial(getTaskMaterial(endTraceTaskVO.getTaskId(), false));
        endTraceTaskVO.setOutputMaterial(getTaskMaterial(endTraceTaskVO.getTaskId(), true));
        endTraceTaskVO.setEquip(getTaskEquip(endTraceTaskVO.getTaskId()));
        taskCache.put(endTraceTaskVO.getTaskId(), endTraceTaskVO);
        path.setEnd(endTraceTaskVO);
    }

    /**
     * 处理开始节点
     *
     * @param traceTaskVO
     * @param taskCache
     * @param pathMap
     * @param path
     * <AUTHOR>
     * @date 2025/3/25 16:55
     */
    private void dealStartNode(TraceTaskVO traceTaskVO,
                               ConcurrentHashMap<String, TraceTaskVO> taskCache,
                               Map<String, Object> pathMap,
                               TracePathVO path) {
        Map<String, Object> startMap = (Map) pathMap.get("s");
        Map<String, Object> startProperties = (Map) startMap.get("properties");
        Map<String, Object> startTask = (Map) startProperties.get("task");
        TraceTaskVO startTraceTaskVO;
        if (taskCache.containsKey(startTask.get("taskId"))) {
            startTraceTaskVO = taskCache.get(startTask.get("taskId"));
            path.setStart(startTraceTaskVO);
            return;
        }
        Map<String, Object> dataMap = Maps.newHashMap();
        startTask.forEach((k, v) -> {
            dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
        });
        startTraceTaskVO = JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceTaskVO.class);
        startTraceTaskVO.setInputMaterial(getTaskMaterial(startTraceTaskVO.getTaskId(), false));
        startTraceTaskVO.setOutputMaterial(getTaskMaterial(startTraceTaskVO.getTaskId(), true));
        startTraceTaskVO.setEquip(getTaskEquip(startTraceTaskVO.getTaskId()));
        taskCache.put(startTraceTaskVO.getTaskId(), startTraceTaskVO);
        path.setStart(startTraceTaskVO);
    }

    /**
     * 获取产出物料的任务
     *
     * @param query 查询条件
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private List<TraceTaskVO> getSelectTasks(QualityTraceQuery query) {
        List<TraceTaskVO> result = Lists.newArrayList();
        String nGQL = StringUtils.isBlank(query.getPlantId()) ?
                CommonNGQLConstants.getQueryTaskByOutputMaterialCodeNGQL(query.getMaterialCode()) :
                CommonNGQLConstants.getQueryTaskByOutputMaterialCodeAndPlantIdNGQL(query.getPlantId(), query.getMaterialCode());
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> taskMap : execute) {
            Map<String, Object> t = (Map) taskMap.get("t");
            Map<String, Object> properties = (Map) t.get("properties");
            Map<String, Object> task = (Map) properties.get("task");
            Map<String, Object> dataMap = Maps.newHashMap();
            task.forEach((k, v) -> {
                dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
            });
            TraceTaskVO traceTaskVO = JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceTaskVO.class);
            traceTaskVO.setInputMaterial(getTaskMaterial(traceTaskVO.getTaskId(), false));
            traceTaskVO.setOutputMaterial(getTaskMaterial(traceTaskVO.getTaskId(), true));
            traceTaskVO.setEquip(getTaskEquip(traceTaskVO.getTaskId()));
            result.add(traceTaskVO);
        }
        return result;
    }

    /**
     * 获取任务设备
     *
     * @param taskId 任务id
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private List<TraceEquipVO> getTaskEquip(String taskId) {
        List<TraceEquipVO> result = Lists.newArrayList();
        String nGQL = CommonNGQLConstants.getQueryTaskEquipByTaskIdNGQL(taskId);
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> taskMap : execute) {
            Map<String, Object> e = (Map) taskMap.get("e");
            Map<String, Object> properties = (Map) e.get("properties");
            Map<String, Object> equip = (Map) properties.get("equip");
            Map<String, Object> dataMap = Maps.newHashMap();
            equip.forEach((k, v) -> {
                dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
            });
            TraceEquipVO traceEquipVO = JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceEquipVO.class);
            result.add(traceEquipVO);
        }
        return result;
    }


    /**
     * 获取任务物料
     *
     * @param taskId 任务id
     * @param type   true:产出物料 false:输入物料
     * <AUTHOR>
     * @date 2025/3/24 14:35
     */
    private List<TraceMaterialVO> getTaskMaterial(String taskId, boolean type) {
        List<TraceMaterialVO> result = Lists.newArrayList();
        String nGQL = type ? CommonNGQLConstants.getQueryTaskOutputMaterialByTaskIdNGQL(taskId)
                : CommonNGQLConstants.getQueryTaskInputMaterialByTaskIdNGQL(taskId);
        List<Map<String, Object>> execute = NebulaUtil.execute(nGQL);
        for (Map<String, Object> taskMap : execute) {
            Map<String, Object> m = (Map) taskMap.get("m");
            Map<String, Object> properties = (Map) m.get("properties");
            Map<String, Object> material = (Map) properties.get("material");
            Map<String, Object> dataMap = Maps.newHashMap();
            material.forEach((k, v) -> {
                dataMap.put(k, NebulaUtil.convertNebulaValue((ValueWrapper) v));
            });
            result.add(JsonUtils.fromJson(JsonUtils.toJson(dataMap), TraceMaterialVO.class));
        }
        return result;
    }
}
